#!/usr/bin/bash
##logo
#5da418b4ef4821bd88037bd9dd8b1a86c1430294704790a1669047963236d06e
#
#afea54f05bbc0e1369857fe23babfdff6a8d5894cd36a46d2e51151027d45f39
#colony-x86_64-unknown-linux-musl v0.2.3
#Colony CLI v0.2.3 x86_64 Linux binary. Using colonylib=v0.4.3 and autonomi=v0.5.0.
#
#7022594f93821dfd9481ac8f113cff5d51232061522d1b472debbec21c5728cb
#colonyd-x86_64-unknown-linux-musl v0.2.3
#Colony Daemon v0.2.3 x86_64 Linux binary. Using colonylib=v0.4.3 and autonomi=v0.5.0.
#
#443035d932927927ff7747cfd9b587c0285147c443d6768f8ac67ca45814529f
#colony-x86_64-pc-windows-msvc.exe v0.2.3
#Colony CLI v0.2.3 x86_64 Windows binary. Using colonylib=v0.4.3 and autonomi=v0.5.0.
#
#
#1a5b514664a787e858ac7ca6773248bb35a1817a3aef155ff0cb3f72a9ed5a20
#colonyd-x86_64-pc-windows-msvc.exe v0.2.3
#Colony Daemon v0.2.3 x86_64 Windows binary. Using colonylib=v0.4.3 and autonomi=v0.5.0.
#
#
#10d6dc280937d3cbdfceddd959582c3f1c1ac9ccc7d70d9ea2fe30a2cbf12188
#colony-aarch64-apple-darwin v0.2.3
#Colony CLI v0.2.3 aarch64 binary. Using colonylib=v0.4.3 and autonomi=v0.5.0.
#
#
#975af7e401cbce278c34006e2214843320327ffad41174ad4965b047d4134160
#colonyd-aarch64-apple-darwin v0.2.3
#Colony Daemon v0.2.3 x86_64 aarch64 binary. Using colonylib=v0.4.3 and autonomi=v0.5.0.

# alpha ETH wallet: d1d4b50cc66326a8f6ce00be7a7f4682ecd5056b911b98719cec06a32c64330b
#setenv SECRET_KEY d1d4b50cc66326a8f6ce00be7a7f4682ecd5056b911b98719cec06a32c64330b

cargo run --bin colonyd -- -n local -p 3001 -d /home/<USER>/.local/share/colony_1 --pass pass:password
# instance 1: woman identify dash tumble stairs claim remove absent okay little bulk version
# 1st pod: b0eefffd5a279206ebf153e4ee6dece61eebdf922c783bd30cf2868242ba15eb7b569e3f210a342ae3d10d8efad6a6ed
# -> add "2nd pod" pod ref

cargo run --bin colonyd -- -n local -p 3002 -d /home/<USER>/.local/share/colony_2 --pass pass:password
# instance 2: army pioneer fence climb erase quit stomach park poem waste okay ordinary
# 2nd pod: 846145ef9e4502af38e49ae155be23273e9254670993e48b7f5737ee8173639e890a3f0e162dfc6aaf901fc3b2793336
# -> add "3rd pod" pod ref

cargo run --bin colonyd -- -n local -p 3003 -d /home/<USER>/.local/share/colony_3 --pass pass:password
# instance 3: vehicle focus calm brand unique oak cry spice oval wheat all trick
# 3rd pod: a865b779db4892651b215ef30618506c861d0f3e8283dfc17612a4d3fe1307a39bc15dbe5d024cb58fcceacde8bad807
# -> add "4th pod" pod ref
# 4th pod: ab9507468cf4a367f0ae98936bed67ed161a1463224dfc8b85a53bbe31304b4b4ddd354256c9d1f7e7d1e4776036995e
# -> add "genesis pod" pod ref

# b0eefffd5a279206ebf153e4ee6dece61eebdf922c783bd30cf2868242ba15eb7b569e3f210a342ae3d10d8efad6a6ed
cargo run --bin colony -- -p 3001 add pod "1st pod"
# a232f598f07e4f201a425ae42f8d6062685190ce5312af4815c43844461a19fd063d30d8a76d52732e087ece8f050995
cargo run --bin colony -- -p 3002 add pod "2nd pod"
# b1da42af46dc29b7bdd68ce3b8efc14ee2b51be08b32cd24562b684af2dfad62fff3825c75a7ed94770d74260d369635
cargo run --bin colony -- -p 3003 add pod "3rd pod"
# a44baca91c455654175d8188744e6a2c249a41a1b7ae151e63458a5ee8ca9198f08ef010891a81b5ed0d7958466359f5
cargo run --bin colony -- -p 3003 add pod "4th pod"

# point to 2nd pod
cargo run --bin colony -- -p 3001 add ref "1st pod" a232f598f07e4f201a425ae42f8d6062685190ce5312af4815c43844461a19fd063d30d8a76d52732e087ece8f050995
# point to 3rd pod
cargo run --bin colony -- -p 3002 add ref "2nd pod" b1da42af46dc29b7bdd68ce3b8efc14ee2b51be08b32cd24562b684af2dfad62fff3825c75a7ed94770d74260d369635
# point to 4th pod
cargo run --bin colony -- -p 3003 add ref "3rd pod" a44baca91c455654175d8188744e6a2c249a41a1b7ae151e63458a5ee8ca9198f08ef010891a81b5ed0d7958466359f5
# point to 1st pod
cargo run --bin colony -- -p 3003 add ref "4th pod" b0eefffd5a279206ebf153e4ee6dece61eebdf922c783bd30cf2868242ba15eb7b569e3f210a342ae3d10d8efad6a6ed

#1 --> 2 --> 3 --> 4
#|-----------------^

cargo run --bin colony -- -p 3001 upload
cargo run --bin colony -- -p 3002 upload
cargo run --bin colony -- -p 3003 upload

cargo run --bin colony -- -p 3001 refresh --depth 0
cargo run --bin colony -- -p 3002 refresh --depth 0
cargo run --bin colony -- -p 3003 refresh --depth 0

cargo run --bin colony -- -p 3001 search text pod
cargo run --bin colony -- -p 3002 search text pod
cargo run --bin colony -- -p 3003 search text pod

###################################################################
###################################################################
###################################################################

# colony main seedphrase: excite quantum false crew tongue witness alarm thank topic public skin effort
# Genesis pod address: aaa518a2cf8260f6bebc769c16b8147ea215adf569696497b7fc1f250823d89a49990187e83fc0f9ae1cf3d44afb7dce
# alpha secret key: d1d4b50cc66326a8f6ce00be7a7f4682ecd5056b911b98719cec06a32c64330b
# original ETH balance: 0.00393
# original ANT balance: 4116.47805

# on wifi with colonylib v0.3.1 and autonomi v0.4.6:
# ~5 minutes for initial upload of 2 pods
# ~30 seconds for updates to a pod06/24/2025
# ~4 minutes for refresh, 2 pods

# on wifi with colonylib v0.4.3 and autonomi v0.5.0:
# ~3 1/2 minutes for initial upload of 2 pods
# ~30 seconds for updates to a pod
# ~30 seconds for refresh, 2 pods

# add a pod
colony add pod "Colony Genesis Pod"

# add this data to pods
colony put "Colony Genesis Pod" c859818c623ce4fc0899c2ab43061b19caa0b0598eec35ef309dbe50c8af8d59 '{\
  "@context": {"schema": "http://schema.org/"},\
  "@type": "schema:MediaObject",\
  "@id": "ant://c859818c623ce4fc0899c2ab43061b19caa0b0598eec35ef309dbe50c8af8d59",\
  "schema:name": "BegBlag.mp3",\
  "schema:description": "Beg Blag and Steal",\
  "schema:encodingFormat": "audio/mpeg"\
}'

colony put "Colony Genesis Pod" 350a5b3de1482e05a0c12ce7f434504d7372961036f692565c6fbf0511b0b800 '{\
  "@context": {"schema": "http://schema.org/"},\
  "@type": "schema:MediaObject",\
  "@id": "ant://350a5b3de1482e05a0c12ce7f434504d7372961036f692565c6fbf0511b0b800",\
  "schema:name": "AnarchyInTheSouthside.mp3",\
  "schema:encodingFormat": "audio/mpeg"\
}'

colony put "Colony Genesis Pod" 076f7b3c16ced138b794084f35d581003046e363fca82fd851556af4296a2541 '{\
  "@context": {"schema": "http://schema.org/"},\
  "@type": "schema:MediaObject",\
  "@id": "ant://076f7b3c16ced138b794084f35d581003046e363fca82fd851556af4296a2541",\
  "schema:name": "Best_CD_Ever.mp3",\
  "schema:encodingFormat": "audio/mpeg"\
}'

colony put "Colony Genesis Pod" cc62ca079e8c3e649a134c0ff80da86c393ad16d6c40c002e0643afd7c51d55b '{\
  "@context": {"schema": "http://schema.org/"},\
  "@type": "schema:MediaObject",\
  "@id": "ant://cc62ca079e8c3e649a134c0ff80da86c393ad16d6c40c002e0643afd7c51d55b",\
  "schema:name": "Deep_Feelings_Mix.mp3",\
  "schema:encodingFormat": "audio/mpeg"\
}'

colony put "Colony Genesis Pod" 4d32050ad7972af55f3f88ae6c3e0366e7bebc816a2c9990516d31a28713323f '{\
  "@context": {"schema": "http://schema.org/"},\
  "@type": "schema:MediaObject",\
  "@id": "ant://4d32050ad7972af55f3f88ae6c3e0366e7bebc816a2c9990516d31a28713323f",\
  "schema:name": "Patosh-RoughNight.mp3",\
  "schema:encodingFormat": "audio/mpeg"\
}'

colony put "Colony Genesis Pod" bfa02e3c6af8c2b5b515a80a10f7a3389442b9e60a2419e447e42ae3e7f93cc6 '{\
  "@context": {"schema": "http://schema.org/"},\
  "@type": "schema:MediaObject",\
  "@id": "ant://bfa02e3c6af8c2b5b515a80a10f7a3389442b9e60a2419e447e42ae3e7f93cc6",\
  "schema:name": "Big Country live at Glasgow Apollo In A Big Country.MP3",\
  "schema:encodingFormat": "audio/mpeg",\
  "schema:description": "Live performance by Big Country at Glasgow Apollo"\
}'

colony put "Colony Genesis Pod" 1b5a77a34e172ea3c6596b17eb33bc4e64aa8d2ed378d8be9a52f4a37083eb1f '{\
  "@context": {"schema": "http://schema.org/"},\
  "@type": "schema:MediaObject",\
  "@id": "ant://1b5a77a34e172ea3c6596b17eb33bc4e64aa8d2ed378d8be9a52f4a37083eb1f",\
  "schema:encodingFormat": "audio/mpeg",\
  "schema:description": "A song @NAFO_Radio wrote about being a little kid",\
  "schema:creator": "@NAFO_Radio"\
}'

## Videos
colony put "Colony Genesis Pod" 5a3fab6764916342f015c67b7856ba8ff2c0e711827ce18abdb3075f358c5237 '{\
  "@context": {"schema": "http://schema.org/"},\
  "@type": "schema:VideoObject",\
  "@id": "ant://5a3fab6764916342f015c67b7856ba8ff2c0e711827ce18abdb3075f358c5237",\
  "schema:name": "An_Introduction_To_The_SafeNetwork.mp4",\
  "schema:encodingFormat": "video/mp4",\
  "schema:description": "An introduction to the Safe Network"\
}'

colony put "Colony Genesis Pod" 5bc2a0a0f309e55c83c98d033a1711b10338eba10c8696a4af65a39e70b1e6ae '{\
  "@context": {"schema": "http://schema.org/"},\
  "@type": "schema:VideoObject",\
  "@id": "ant://5bc2a0a0f309e55c83c98d033a1711b10338eba10c8696a4af65a39e70b1e6ae",\
  "schema:name": "autonomi.mp4",\
  "schema:encodingFormat": "video/mp4"\
}'

colony put "Colony Genesis Pod" 4c983cd101dfc42efe705374b6598e2c3090a4893fd8455ff1f97dc149fb22bb '{\
  "@context": {"schema": "http://schema.org/"},\
  "@type": "schema:VideoObject",\
  "@id": "ant://4c983cd101dfc42efe705374b6598e2c3090a4893fd8455ff1f97dc149fb22bb",\
  "schema:name": "Metropolis (1927)",\
  "schema:encodingFormat": "video/mp4",\
  "schema:description": "Metropolis (1927) DVD",\
  "schema:contentSize": "3.0 GB",\
  "schema:dateCreated": "1927"\
}'

colony put "Colony Genesis Pod" a8fe9929f861cf4212197ed850d01b8034e1a521665ac6bb14a791f4da56276b '{\
  "@context": {"schema": "http://schema.org/"},\
  "@type": "schema:VideoObject",\
  "@id": "ant://a8fe9929f861cf4212197ed850d01b8034e1a521665ac6bb14a791f4da56276b",\
  "schema:encodingFormat": "video/mp4",\
  "schema:description": "mp4 video of a Dragon capsule re-entering over San Francisco on 24-May-2025",\
  "schema:contentLocation": "San Francisco",\
  "schema:dateCreated": "2025-05-24"\
}'

colony put "Colony Genesis Pod" 031f18a68ab3a3c63d75fa0a484f406dd8d3dabb1d009d59d9085eb8f332cc64 '{\
  "@context": {"schema": "http://schema.org/"},\
  "@type": "schema:VideoObject",\
  "@id": "ant://031f18a68ab3a3c63d75fa0a484f406dd8d3dabb1d009d59d9085eb8f332cc64",\
  "schema:name": "VUKOVI-LaDiDa.mp4",\
  "schema:encodingFormat": "video/mp4",\
  "schema:creator": "VUKOVI"\
}'

## Linux ISOs
colony put "Colony Genesis Pod" e7bb1b87c1f0e07cdb76ba5e82a425a8da712940c2d3553aa6791494e92aa54d '{\
  "@context": {"schema": "http://schema.org/"},\
  "@type": "schema:SoftwareApplication",\
  "@id": "ant://e7bb1b87c1f0e07cdb76ba5e82a425a8da712940c2d3553aa6791494e92aa54d",\
  "schema:name": "ubuntu-16.04.6-desktop-i386.iso",\
  "schema:operatingSystem": "Ubuntu 16.04.6",\
  "schema:applicationCategory": "Operating System",\
  "schema:processorRequirements": "i386"\
}'

colony put "Colony Genesis Pod" 9875177d76c9768edbabe048ad2b2846b8a9de0286bd5e1097813cc0dc75128f '{\
  "@context": {"schema": "http://schema.org/"},\
  "@type": "schema:SoftwareApplication",\
  "@id": "ant://9875177d76c9768edbabe048ad2b2846b8a9de0286bd5e1097813cc0dc75128f",\
  "schema:name": "archlinux-2025.04.01-x86_64.iso",\
  "schema:operatingSystem": "Arch Linux",\
  "schema:applicationCategory": "Operating System",\
  "schema:processorRequirements": "x86_64",\
  "schema:contentSize": "1.2 GB",\
  "schema:dateCreated": "2025-04-01"\
}'

colony put "Colony Genesis Pod" ccd03711ac7db31e78ed197a02f7dd2c424af6ff2b3c77f63fa7a23720438e17 '{\
  "@context": {"schema": "http://schema.org/"},\
  "@type": "schema:SoftwareApplication",\
  "@id": "ant://ccd03711ac7db31e78ed197a02f7dd2c424af6ff2b3c77f63fa7a23720438e17",\
  "schema:name": "linuxmint-22.1-cinnamon-64bit.iso",\
  "schema:operatingSystem": "Linux Mint 22.1 Cinnamon",\
  "schema:applicationCategory": "Operating System",\
  "schema:processorRequirements": "64-bit",\
  "schema:contentSize": "2.8 GB"\
}'

colony put "Colony Genesis Pod" 142925b90fc5f6e38807713303fbdc91bfcd893a3c0f597e53764087fd70b8ed '{\
  "@context": {"schema": "http://schema.org/"},\
  "@type": "schema:SoftwareApplication",\
  "@id": "ant://142925b90fc5f6e38807713303fbdc91bfcd893a3c0f597e53764087fd70b8ed",\
  "schema:name": "ubuntu-24.04.2-desktop-amd64.iso",\
  "schema:operatingSystem": "Ubuntu 24.04.2",\
  "schema:applicationCategory": "Operating System",\
  "schema:processorRequirements": "amd64",\
  "schema:contentSize": "5.8 GB"\
}'

## Images
colony put "Colony Genesis Pod" 5da418b4ef4821bd88037bd9dd8b1a86c1430294704790a1669047963236d06e '{\
  "@context": {"schema": "http://schema.org/"},\
  "@type": "schema:ImageObject",\
  "@id": "ant://5da418b4ef4821bd88037bd9dd8b1a86c1430294704790a1669047963236d06e",\
  "schema:name": "logo-96x96.png",\
  "schema:encodingFormat": "image/png",\
  "schema:description": "Colony logo 96x96 PNG"\
}'

colony put "Colony Genesis Pod" 63059aabaa28fb168ee45598a4540fb560e6f11df5e53a9dfac39fa413147310 '{\
  "@context": {"schema": "http://schema.org/"},\
  "@type": "schema:ImageObject",\
  "@id": "ant://63059aabaa28fb168ee45598a4540fb560e6f11df5e53a9dfac39fa413147310",\
  "schema:name": "lions-chair-front.jpg",\
  "schema:encodingFormat": "image/jpeg",\
  "schema:description": "Front view of lions chair"\
}'

colony put "Colony Genesis Pod" 6f502ed75ea1719b5bd703784a2e3e710728e13fa41702ea590e1b8b515b22ab '{\
  "@context": {"schema": "http://schema.org/"},\
  "@type": "schema:ImageObject",\
  "@id": "ant://6f502ed75ea1719b5bd703784a2e3e710728e13fa41702ea590e1b8b515b22ab",\
  "schema:name": "IA.jpg",\
  "schema:encodingFormat": "image/jpeg",\
  "schema:description": "Logo for Idle Asset"\
}'

colony put "Colony Genesis Pod" 4467c38f2591ddf840161dfd8536bfff594be4e455bf2630e841de846d49029a '{\
  "@context": {"schema": "http://schema.org/"},\
  "@type": "schema:ImageObject",\
  "@id": "ant://4467c38f2591ddf840161dfd8536bfff594be4e455bf2630e841de846d49029a",\
  "schema:description": "A drawing of an ant girl",\
  "schema:encodingFormat": "image/jpeg"\
}'

colony put "Colony Genesis Pod" b9681387e1695bfb3ac1d73be5235db6bd852ef2b071e853ec19911928b12095 '{\
  "@context": {"schema": "http://schema.org/"},\
  "@type": "schema:ImageObject",\
  "@id": "ant://b9681387e1695bfb3ac1d73be5235db6bd852ef2b071e853ec19911928b12095",\
  "schema:description": "Image of venus transitting the sun on 06-Jun-2012",\
  "schema:encodingFormat": "image/jpeg",\
  "schema:dateCreated": "2012-06-06"\
}'

colony put "Colony Genesis Pod" 1215a9a77405e11e3197239a509acc557f67b0e35b62e22643f67dce94b430a2 '{\
  "@context": {"schema": "http://schema.org/"},\
  "@type": "schema:ImageObject",\
  "@id": "ant://1215a9a77405e11e3197239a509acc557f67b0e35b62e22643f67dce94b430a2",\
  "schema:description": "Image of total solar eclipse on 21-Aug-2017",\
  "schema:encodingFormat": "image/jpeg",\
  "schema:dateCreated": "2017-08-21"\
}'

## Books
colony put "Colony Genesis Pod" dcb90722cd6c7a3c66527fd8401970cad21cfc61f17e37abd421414ca26900f6 '{\
  "@context": {"schema": "http://schema.org/"},\
  "@type": "schema:Book",\
  "@id": "ant://dcb90722cd6c7a3c66527fd8401970cad21cfc61f17e37abd421414ca26900f6",\
  "schema:description": "A collection of books by Richard Feynman",\
  "schema:author": "Richard Feynman"\
}'

colony put "Colony Genesis Pod" f7e59ac54fb81d195080787ba5e3125c767324f1b0cc2f51fac069652816be1d '{\
  "@context": {"schema": "http://schema.org/"},\
  "@type": "schema:Book",\
  "@id": "ant://f7e59ac54fb81d195080787ba5e3125c767324f1b0cc2f51fac069652816be1d",\
  "schema:description": "A collection of books by Christopher Brookmyre",\
  "schema:author": "Christopher Brookmyre"\
}'

## Programs and Scripts
colony put "Colony Genesis Pod" 3ffa427480548a98ef71ea7d7f937dfc730db1598e0928027a47475798a08748 '{\
  "@context": {"schema": "http://schema.org/"},\
  "@type": "schema:SoftwareSourceCode",\
  "@id": "ant://3ffa427480548a98ef71ea7d7f937dfc730db1598e0928027a47475798a08748",\
  "schema:name": "chunk_checker.sh",\
  "schema:programmingLanguage": "Shell Script"\
}'

colony put "Colony Genesis Pod" 1beecef69f9418aa82b31095f568f60e7073d0fd5c5944ba3b65a2f17e1447e6 '{\
  "@context": {"schema": "http://schema.org/"},\
  "@type": "schema:VideoGame",\
  "@id": "ant://1beecef69f9418aa82b31095f568f60e7073d0fd5c5944ba3b65a2f17e1447e6",\
  "schema:name": "Doukutsu Monogatari",\
  "schema:description": "A freeware game",\
  "schema:applicationCategory": "Game"\
}'

colony put "Colony Genesis Pod" a88343401d7c67df13cf71807a810f263f72086777d5ebaffeb2eb12b7b533ae '{\
  "@context": {"schema": "http://schema.org/"},\
  "@type": "schema:VideoGame",\
  "@id": "ant://a88343401d7c67df13cf71807a810f263f72086777d5ebaffeb2eb12b7b533ae",\
  "schema:name": "Pubkemon Go",\
  "schema:description": "Lo-fi AR pub crawl html game",\
  "schema:applicationCategory": "Game",\
  "schema:genre": "Augmented Reality"\
}'

colony put "Colony Genesis Pod" 380f6a9c4e7a03fea21393bb7caeea91f70e1cc69ca55c9c791030ab1423a08e '{\
  "@context": {"schema": "http://schema.org/"},\
  "@type": "schema:SoftwareApplication",\
  "@id": "ant://380f6a9c4e7a03fea21393bb7caeea91f70e1cc69ca55c9c791030ab1423a08e",\
  "schema:name": "Formicaio",\
  "schema:description": "Latest Formicaio binary for Linux",\
  "schema:operatingSystem": "Linux",\
  "schema:applicationCategory": "Application"\
}'

## dweb websites and apps
colony put "Colony Genesis Pod" a447871043968be2be1628584026cad30b824009a30eab43db3ee6dd8c0990051c27160cc8d1662da763d57c41c091f6 '{\
  "@context": {"schema": "http://schema.org/", "dweb": "ant://dweb/v1/"},\
  "@type": "dweb:WebSite",\
  "@id": "ant://a447871043968be2be1628584026cad30b824009a30eab43db3ee6dd8c0990051c27160cc8d1662da763d57c41c091f6",\
  "schema:name": "Friends",\
  "schema:description": "Friends - The messenger you will never want to move away from"\
}'

colony put "Colony Genesis Pod" 99e3b8df52814b379e216caf797426071000905a2cd93a9f5e90eef2b32517a9ec1ef0bfe27d79360014fd97639ac612 '{\
  "@context": {"schema": "http://schema.org/", "dweb": "ant://dweb/v1/"},\
  "@type": "dweb:WebSite",\
  "@id": "ant://99e3b8df52814b379e216caf797426071000905a2cd93a9f5e90eef2b32517a9ec1ef0bfe27d79360014fd97639ac612",\
  "schema:name": "Atlas",\
  "schema:description": "Atlas - Things on Autonomi"\
}'

colony put "Colony Genesis Pod" 80ef65377028522309551ff549e015d82572b7ae306e029fce702ebffde4167c0b67049ad062174055fbb2526f237cc7 '{\
  "@context": {"schema": "http://schema.org/", "dweb": "ant://dweb/v1/"},\
  "@type": "dweb:WebSite",\
  "@id": "ant://80ef65377028522309551ff549e015d82572b7ae306e029fce702ebffde4167c0b67049ad062174055fbb2526f237cc7",\
  "schema:name": "Awesome",\
  "schema:description": "Awesome places on Autonomi"\
}'

colony put "Colony Genesis Pod" b6da6740bc5394f9ac0e6a6fa5a42f7f587d3aeaa48fd23ae9a45bef95b571a32429b0353148aa9e04f17cd6da57d179 '{\
  "@context": {"schema": "http://schema.org/", "dweb": "ant://dweb/v1/"},\
  "@type": "dweb:WebSite",\
  "@id": "ant://b6da6740bc5394f9ac0e6a6fa5a42f7f587d3aeaa48fd23ae9a45bef95b571a32429b0353148aa9e04f17cd6da57d179",\
  "schema:name": "Billboard",\
  "schema:description": "A community billboard"\
}'

colony put "Colony Genesis Pod" a27b3fdb495870ace8f91005223998dc675c8e1bceb50bac66c993bb720a013c9f83d7a46e6d0daecbb3530d5249e587 '{\
  "@context": {"schema": "http://schema.org/", "dweb": "ant://dweb/v1/"},\
  "@type": "dweb:WebSite",\
  "@id": "ant://a27b3fdb495870ace8f91005223998dc675c8e1bceb50bac66c993bb720a013c9f83d7a46e6d0daecbb3530d5249e587",\
  "schema:name": "Scratchchat",\
  "schema:description": "Scratchpad based chat application"\
}'

colony put "Colony Genesis Pod" 95be239165b7016b7f6dada20134438e038d0456bff04ec37943e95742726854225aa03faeed4e7bbd96f5383a8f9448 '{\
  "@context": {"schema": "http://schema.org/", "dweb": "ant://dweb/v1/"},\
  "@type": "dweb:WebSite",\
  "@id": "ant://95be239165b7016b7f6dada20134438e038d0456bff04ec37943e95742726854225aa03faeed4e7bbd96f5383a8f9448",\
  "schema:name": "Toast",\
  "schema:description": "The Internet Burned My Toast Again - A HappyBeing Blog"\
}'

colony put "Colony Genesis Pod" 1fac0b4e342802a4aff9a43a7427f0ac989475d4a5a6ff566445366b344cd063 '{\
  "@context": {"schema": "http://schema.org/", "dweb": "ant://dweb/v1/"},\
  "@type": "dweb:WebSite",\
  "@id": "ant://1fac0b4e342802a4aff9a43a7427f0ac989475d4a5a6ff566445366b344cd063",\
  "schema:name": "AutoBoy",\
  "schema:description": "safemedia Nintendo Gameboy Games Site"\
}'

colony put "Colony Genesis Pod" b719b34d662b670cc2f7b2ee89a1d33293ffb36d1dddadc807527a8913cb52ea7eb9b9d22293454d2e612ccfab4400a6 '{\
  "@context": {"schema": "http://schema.org/", "dweb": "ant://dweb/v1/"},\
  "@type": "dweb:WebSite",\
  "@id": "ant://b719b34d662b670cc2f7b2ee89a1d33293ffb36d1dddadc807527a8913cb52ea7eb9b9d22293454d2e612ccfab4400a6",\
  "schema:name": "vroom",\
  "schema:description": "A big noise by Southside"\
}'

colony put "Colony Genesis Pod" 923780b6a2ad82d6290263330e32d82942dcf199b4801092053bed69c40e34ad1b3aee3368819ea1c2b03b82bee06574 '{\
  "@context": {"schema": "http://schema.org/", "dweb": "ant://dweb/v1/"},\
  "@type": "dweb:WebSite",\
  "@id": "ant://923780b6a2ad82d6290263330e32d82942dcf199b4801092053bed69c40e34ad1b3aee3368819ea1c2b03b82bee06574",\
  "schema:name": "KnightStudios",\
  "schema:description": "A complex website template by aatonnomicc"\
}'

#wget https://github.com/zettawatt/colony-utils/releases/latest/download/colonyd-x86_64-unknown-linux-musl
#wget https://github.com/zettawatt/colony-utils/releases/latest/download/colony-x86_64-unknown-linux-musl
#wget https://github.com/zettawatt/colony-utils/releases/latest/download/colonyd-x86_64-pc-windows-msvc.exe
#wget https://github.com/zettawatt/colony-utils/releases/latest/download/colony-x86_64-pc-windows-msvc.exe
#wget https://github.com/zettawatt/colony-utils/releases/latest/download/colonyd-x86_64-pc-windows-msvc.exe
#wget https://github.com/zettawatt/colony-utils/releases/latest/download/colony-x86_64-pc-windows-msvc.exe
#wget https://github.com/zettawatt/colony-utils/releases/latest/download/colonyd-aarch64-apple-darwin
#wget https://github.com/zettawatt/colony-utils/releases/latest/download/colony-aarch64-apple-darwin
#
#ant file upload -p --max-fee-per-gas market colonyd-x86_64-unknown-linux-musl
#ant file upload -p --max-fee-per-gas market colony-x86_64-unknown-linux-musl
#ant file upload -p --max-fee-per-gas market colonyd-x86_64-pc-windows-msvc.exe
#ant file upload -p --max-fee-per-gas market colony-x86_64-pc-windows-msvc.exe
#ant file upload -p --max-fee-per-gas market colony-aarch64-apple-darwin
#ant file upload -p --max-fee-per-gas market colonyd-aarch64-apple-darwin

colony put "Colony Genesis Pod" afea54f05bbc0e1369857fe23babfdff6a8d5894cd36a46d2e51151027d45f39 '{\
  "@context": {"schema": "http://schema.org/"},\
  "@type": "schema:SoftwareApplication",\
  "@id": "ant://afea54f05bbc0e1369857fe23babfdff6a8d5894cd36a46d2e51151027d45f39",\
  "schema:name": "colony-x86_64-unknown-linux-musl v0.2.3",\
  "schema:description": "Colony CLI v0.2.3 x86_64 Linux binary. Using colonylib=v0.4.3 and autonomi=v0.5.0.",\
  "schema:operatingSystem": "Linux",\
  "schema:contentSize": "8MB",\
  "schema:applicationCategory": "Application"\
}'

colony put "Colony Genesis Pod" 7022594f93821dfd9481ac8f113cff5d51232061522d1b472debbec21c5728cb '{\
  "@context": {"schema": "http://schema.org/"},\
  "@type": "schema:SoftwareApplication",\
  "@id": "ant://7022594f93821dfd9481ac8f113cff5d51232061522d1b472debbec21c5728cb",\
  "schema:name": "colonyd-x86_64-unknown-linux-musl v0.2.3",\
  "schema:description": "Colony Daemon v0.2.3 x86_64 Linux binary. Using colonylib=v0.4.3 and autonomi=v0.5.0.",\
  "schema:operatingSystem": "Linux",\
  "schema:contentSize": "41MB",\
  "schema:applicationCategory": "Application"\
}'

colony put "Colony Genesis Pod" 443035d932927927ff7747cfd9b587c0285147c443d6768f8ac67ca45814529f '{\
  "@context": {"schema": "http://schema.org/"},\
  "@type": "schema:SoftwareApplication",\
  "@id": "ant://443035d932927927ff7747cfd9b587c0285147c443d6768f8ac67ca45814529f",\
  "schema:name": "colony-x86_64-pc-windows-msvc.exe v0.2.3",\
  "schema:description": "Colony CLI v0.2.3 x86_64 Windows binary. Using colonylib=v0.4.3 and autonomi=v0.5.0.",\
  "schema:operatingSystem": "Windows",\
  "schema:contentSize": "6MB",\
  "schema:applicationCategory": "Application"\
}'

colony put "Colony Genesis Pod" 1a5b514664a787e858ac7ca6773248bb35a1817a3aef155ff0cb3f72a9ed5a20 '{\
  "@context": {"schema": "http://schema.org/"},\
  "@type": "schema:SoftwareApplication",\
  "@id": "ant://1a5b514664a787e858ac7ca6773248bb35a1817a3aef155ff0cb3f72a9ed5a20",\
  "schema:name": "colonyd-x86_64-pc-windows-msvc.exe v0.2.3",\
  "schema:description": "Colony Daemon v0.2.3 x86_64 Windows binary. Using colonylib=v0.4.3 and autonomi=v0.5.0.",\
  "schema:operatingSystem": "Windows",\
  "schema:contentSize": "31MB",\
  "schema:applicationCategory": "Application"\
}'

colony put "Colony Genesis Pod" 10d6dc280937d3cbdfceddd959582c3f1c1ac9ccc7d70d9ea2fe30a2cbf12188 '{\
  "@context": {"schema": "http://schema.org/"},\
  "@type": "schema:SoftwareApplication",\
  "@id": "ant://10d6dc280937d3cbdfceddd959582c3f1c1ac9ccc7d70d9ea2fe30a2cbf12188",\
  "schema:name": "colony-aarch64-apple-darwin v0.2.3",\
  "schema:description": "Colony CLI v0.2.3 aarch64 binary. Using colonylib=v0.4.3 and autonomi=v0.5.0.",\
  "schema:operatingSystem": "MacOS",\
  "schema:contentSize": "6MB",\
  "schema:applicationCategory": "Application"\
}'

colony put "Colony Genesis Pod" 975af7e401cbce278c34006e2214843320327ffad41174ad4965b047d4134160 '{\
  "@context": {"schema": "http://schema.org/"},\
  "@type": "schema:SoftwareApplication",\
  "@id": "ant://975af7e401cbce278c34006e2214843320327ffad41174ad4965b047d4134160",\
  "schema:name": "colonyd-aarch64-apple-darwin v0.2.3",\
  "schema:description": "Colony Daemon v0.2.3 x86_64 aarch64 binary. Using colonylib=v0.4.3 and autonomi=v0.5.0.",\
  "schema:operatingSystem": "MacOS",\
  "schema:contentSize": "34MB",\
  "schema:applicationCategory": "Application"\
}'

